import glob
import os
import re
from difflib import SequenceMatcher
from typing import List, Optional, Dict, Union, Tuple

import docx

from utils.logger import logger


class WordProcessor:
    """Word文档处理工具类"""

    def __init__(self, data_dir: str = 'data'):
        """
        初始化Word文档处理器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.answer_markers = ['正确答案是：', '正确答案是', '正确的答案是', '正确的答案是：', '正确答案：', '正确答案',
                               '答案', '答案：', '答案是', '答案是：', 'The correct answers are: ']
        self._word_files = None
        self._doc_cache: Dict[str, docx.Document] = {}

    def _preprocess_text(self, text: str) -> str:
        """
        文本预处理

        Args:
            text: 原始文本

        Returns:
            处理后的文本
        """
        # 分离题干和选项，使用更全面的选项标记模式
        # 支持 A. A、 A． A） (A) 等多种格式，以及A-Z的所有选项
        option_patterns = [
            r'[A-Z][\.、．）]',  # A. A、 A． A）
            r'\([A-Z]\)',  # (A)
            r'[A-Z]\s*[\.、．）]',  # A . A 、 A ． A ）（带空格）
        ]

        # 尝试每种模式进行分割
        for pattern in option_patterns:
            parts = re.split(pattern, text)
            if len(parts) > 1:
                # 只保留题干部分（第一部分）
                text = parts[0].strip()
                break

        # 处理换行符
        text = text.replace('\n', ' ').replace('\r', ' ')

        # 合并多个空格
        text = re.sub(r'\s+', ' ', text)
        # 处理括号中的空格
        text = re.sub(r'（\s*）', '（）', text)
        # 处理英文括号中的空格
        text = re.sub(r'\(\s*\)', '()', text)

        # 移除特殊字符，但保留括号和选项标记
        text = re.sub(r'[^\w\s\u4e00-\u9fff\(\)（）A-D]', '', text)

        return text.strip()

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            相似度分数 (0-1)
        """
        return SequenceMatcher(None, text1, text2).ratio()

    def _extract_question_parts(self, text: str) -> List[str]:
        """
        提取题目的关键部分
        
        Args:
            text: 题目文本
            
        Returns:
            题目关键部分列表
        """
        parts = []

        # 首先添加完整的题目
        parts.append(text)

        # 处理带括号的题目
        bracket_pattern = r'[（(][^）)]*[）)]'

        # 处理括号
        bracket_contents = re.findall(bracket_pattern, text)
        clean_question = re.sub(bracket_pattern, '', text).strip()

        # 添加清理后的题目
        if clean_question != text:
            parts.append(clean_question)

        # 提取括号内容
        parts.extend(bracket_contents)

        # 移除括号的版本
        no_bracket = re.sub(bracket_pattern, '', clean_question)
        if no_bracket != clean_question:
            parts.append(no_bracket)

        # 只保留括号内容的版本
        if bracket_contents:
            bracket_only = ' '.join(bracket_contents)
            parts.append(bracket_only)

        # 提取关键词组 - 使用更长的短语
        if len(clean_question) > 20:
            # 提取问句部分
            question_part = re.search(r'[^。？！]+[。？！]', clean_question)
            if question_part:
                parts.append(question_part.group())

            # 提取更长的关键词组，避免过短导致误匹配
            key_phrases = re.findall(r'[^，。？！]{10,30}', clean_question)
            parts.extend(key_phrases)

            # 提取主题词 - 通常是名词短语
            topic_phrases = re.findall(r'[\u4e00-\u9fff]{2,}[安全|威胁|挑战|问题|风险]', clean_question)
            parts.extend(topic_phrases)

            # 提取包含空格的部分（可能是填空题的关键部分）
            blank_parts = re.findall(r'[^\s]+\s+[^\s]+', clean_question)
            parts.extend(blank_parts)

        # 过滤和去重
        unique_parts = []
        for part in parts:
            part = part.strip()
            if len(part) > 5 and part not in unique_parts:  # 增加最小长度要求
                unique_parts.append(part)

        return unique_parts

    def _collect_multiline_answer(self, paragraphs: List, start_idx: int, marker: str, current_text: str) -> str:
        """
        收集多行答案内容

        Args:
            paragraphs: 文档段落列表
            start_idx: 开始收集的段落索引
            marker: 答案标记
            current_text: 包含答案标记的当前行文本

        Returns:
            完整的多行答案
        """
        answer_parts = []

        # 从当前行提取答案的第一部分
        first_part = current_text.split(marker)[-1].strip()
        if first_part:
            answer_parts.append(first_part)

        # 继续收集后续行，直到遇到停止条件
        j = start_idx + 1
        max_lines_to_check = 5  # 限制最多检查5行，避免收集过多内容
        lines_collected = 0

        while j < len(paragraphs) and lines_collected < max_lines_to_check:
            next_text = paragraphs[j].text.strip()
            if not next_text:
                j += 1
                continue

            # 停止条件：遇到新的答案标记
            has_answer_marker = any(marker in next_text for marker in self.answer_markers)
            if has_answer_marker:
                break

            # 停止条件：遇到无效内容标记（在行首或单独出现）
            invalid_markers = ['反馈', '解析', '分析', '提示', '注释', '备注']
            if any(next_text.startswith(invalid) or next_text == invalid for invalid in invalid_markers):
                break

            # 停止条件：遇到新题目的开始模式
            # 1. 以数字开头的题目（如"1."、"2、"等）
            if re.match(r'^\d+[\.、．）]', next_text):
                break

            # 2. 包含题目特征的行（问号结尾且较长）
            if '？' in next_text and len(next_text) > 20:
                break

            # 3. 包含"选择一项"或"选择多项"且在行首或单独一行
            if re.match(r'^选择[一多]项', next_text) or next_text in ['选择一项：', '选择多项：']:
                break

            # 4. 遇到选项标记（A. B. C. D.等）
            option_pattern = r'^[A-Z][\.、．）]|^\([A-Z]\)'
            if re.match(option_pattern, next_text):
                break

            # 5. 行内容过长且包含题目特征（可能是新题目）
            if len(next_text) > 50 and ('（）' in next_text or '()' in next_text):
                break

            # 6. 包含明显的题目结束标记
            if any(marker in next_text for marker in ['正确答案是', '答案是', '答案：']):
                break

            # 7. 遇到"试题"开头的行（新题目开始）
            if re.match(r'^试题\s*\d+', next_text) or next_text.startswith('试题'):
                break

            # 8. 遇到考试系统相关的标记
            exam_markers = ['未作答', '满分', '标记试题', '试题正文', '已作答']
            if any(marker in next_text for marker in exam_markers):
                break

            # 收集有效的答案行
            # 移除行末的逗号和句号，因为我们会重新格式化
            clean_line = next_text.rstrip('，。,.')
            if clean_line and len(clean_line) > 1:
                answer_parts.append(clean_line)
                lines_collected += 1

            j += 1

        # 合并答案部分
        if answer_parts:
            # 使用逗号连接多个答案部分
            full_answer = ','.join(answer_parts)
            # 清理多余的逗号和空格
            full_answer = re.sub(r',+', ',', full_answer)  # 合并多个逗号
            full_answer = re.sub(r'\s*,\s*', ',', full_answer)  # 标准化逗号周围的空格
            return full_answer.strip()

        return ""

    def _search_in_document(self, doc: docx.Document, question_text: str, filename: str) -> Union[
        Optional[str], Tuple[Optional[str], float]]:
        """
        在文档对象中搜索答案

        Args:
            doc: Word文档对象
            question_text: 题目文本
            filename: 文档文件名（用于日志）

        Returns:
            找到的答案和相似度分数，如果未找到则返回None或(None, 0.0)
        """
        try:
            paragraphs = doc.paragraphs
            best_match_idx = -1
            best_match_text = ""
            best_similarity = 0.0
            similarity_threshold = 0.6  # 设置相似度阈值

            # 预处理问题文本
            processed_question = self._preprocess_text(question_text)

            # 完全匹配
            for i, para in enumerate(paragraphs):
                text = para.text.strip()
                if not text:
                    continue

                if question_text in text:
                    best_match_idx = i
                    best_match_text = text
                    best_similarity = 1.0
                    logger.info(f"在文档 {filename} 中找到完全匹配")
                    break

            # 进行预处理和部分匹配
            if best_match_idx == -1:
                question_parts = self._extract_question_parts(processed_question)

                # 按长度排序问题部分，优先匹配较长的部分
                question_parts.sort(key=len, reverse=True)

                for i, para in enumerate(paragraphs):
                    text = para.text.strip()
                    if not text:
                        continue

                    # 预处理段落文本
                    processed_text = self._preprocess_text(text)

                    # 计算整体相似度
                    similarity = self._calculate_similarity(processed_question, processed_text)

                    # 如果整体相似度高于阈值，直接使用
                    if similarity > similarity_threshold and similarity > best_similarity:
                        best_match_idx = i
                        best_match_text = text
                        best_similarity = similarity
                        logger.info(f"在文档 {filename} 中找到高相似度匹配: {similarity:.2f}")
                        continue

                    # 遍历问题部分进行匹配
                    for part in question_parts:
                        if len(part) < 10:  # 忽略过短的部分，避免误匹配
                            continue

                        if part in processed_text:
                            # 计算匹配部分的相似度
                            part_similarity = self._calculate_similarity(part, processed_text)
                            if part_similarity > best_similarity:
                                best_match_idx = i
                                best_match_text = text
                                best_similarity = part_similarity
                                logger.info(f"在文档 {filename} 中找到部分匹配: {part}")
                                break

            # 如果最佳匹配的相似度低于阈值，认为没有找到匹配
            if best_similarity < similarity_threshold:
                logger.info(f"在文档 {filename} 中未找到足够相似的匹配，最佳相似度: {best_similarity:.2f}")
                return None, 0.0

            # 如果找到匹配，搜索答案
            if best_match_idx != -1:
                # 遍历后续段落寻找答案
                j = best_match_idx + 1
                while j < len(paragraphs):
                    current_text = paragraphs[j].text.strip()
                    if not current_text:
                        j += 1
                        continue

                    # 检查是否包含答案标记
                    for marker in self.answer_markers:
                        if marker in current_text:
                            # 首先尝试原有的单行方法
                            answer = current_text.split(marker)[-1].strip()

                            # 清理答案开头的冒号和空格
                            answer = re.sub(r'^[：:]\s*', '', answer)

                            # 如果单行方法没有结果，或者答案看起来不完整（以逗号结尾），尝试多行收集
                            if not answer or (answer and answer.endswith(',')):
                                multiline_answer = self._collect_multiline_answer(paragraphs, j, marker, current_text)
                                # 只有当多行答案明显更好时才使用（包含逗号分隔的多个答案）
                                if multiline_answer and ',' in multiline_answer and len(multiline_answer.split(',')) > 1:
                                    answer = multiline_answer

                                # 如果当前行没有答案内容，检查下一行是否包含另一个答案标记
                                if not answer and j + 1 < len(paragraphs):
                                    next_text = paragraphs[j + 1].text.strip()
                                    if next_text:
                                        # 检查下一行是否包含答案标记
                                        has_next_marker = False
                                        for next_marker in self.answer_markers:
                                            if next_marker in next_text:
                                                has_next_marker = True
                                                break

                                        # 如果下一行不包含答案标记，且不是常见的无效内容，则作为答案
                                        invalid_answers = ['反馈', '解析', '分析', '提示', '注释', '备注']
                                        if not has_next_marker and not any(
                                                invalid in next_text for invalid in invalid_answers):
                                            answer = next_text
                                        # 否则跳过当前标记，继续搜索
                                        else:
                                            continue

                            # 如果找到有效答案，返回
                            if answer and len(answer) > 1:  # 确保答案不是空或单个字符
                                # 检查是否是常见的无效答案
                                invalid_answers = ['反馈', '解析', '分析', '提示', '注释', '备注']
                                if any(invalid in answer for invalid in invalid_answers):
                                    # 跳过这个标记，继续搜索
                                    break

                                # 处理"对"和"错"的特殊情况
                                if answer in ['"对"。', '"错"。', '“对”。', '“错”。']:
                                    answer = ''.join(char for char in answer if char.isalnum() or char.isspace())

                                logger.info(f"从文档 {filename} 中找到答案: {answer} (相似度: {best_similarity:.2f})")
                                return answer.strip(), best_similarity

                    # 继续搜索下一段
                    j += 1

            return None, 0.0

        except Exception as e:
            logger.error(f"搜索文档 {filename} 时发生错误: {str(e)}")
            return None, 0.0

    @property
    def word_files(self) -> List[str]:
        """获取所有Word文档文件路径"""
        if self._word_files is None:
            self._word_files = glob.glob(f'{self.data_dir}/**/*.docx', recursive=True)
            if not self._word_files:
                logger.warning(f"在 {self.data_dir} 目录下未找到任何Word文档")
        return self._word_files

    def load_document(self, filename: str, file_path: str) -> Optional[docx.Document]:
        """
        加载指定的Word文档

        Args:
            filename: 文档文件名
            file_path: 文件全路径

        Returns:
            Word文档对象，如果加载失败则返回None
        """
        # 检查缓存
        if filename in self._doc_cache:
            return self._doc_cache[filename]

        if not os.path.exists(file_path):
            logger.error(f"文档不存在: {file_path}")
            return None

        # 验证文档
        if not self._validate_document(file_path):
            return None

        try:
            # 尝试使用标准方式加载
            doc = docx.Document(file_path)
            # 缓存文档
            self._doc_cache[filename] = doc
            return doc
        except Exception as e:
            logger.error(f"加载文档失败 {file_path}: {str(e)}")

            # 特殊处理各种Word文档损坏错误
            error_msg = str(e).lower()
            if any(error_pattern in error_msg for error_pattern in [
                "there is no item named 'word/null' in the archive",
                "there is no item named 'null' in the archive",
                "bad zipfile",
                "file is not a zip file",
                "corrupt",
                "damaged"
            ]):
                logger.warning(f"文档 {file_path} 可能格式不兼容或已损坏，尝试备用方法加载")
                return self._try_alternative_loading(file_path, filename)

            # 处理其他类型的错误
            elif "permission" in error_msg:
                logger.error(f"文档 {file_path} 权限不足，无法访问")
                return None
            elif "memory" in error_msg:
                logger.error(f"文档 {file_path} 过大，内存不足")
                return None
            return None

    def _try_alternative_loading(self, file_path: str, filename: str) -> Optional[docx.Document]:
        """
        尝试使用备用方法加载损坏的Word文档

        Args:
            file_path: 文档文件路径
            filename: 文档文件名

        Returns:
            Word文档对象，如果加载失败则返回None
        """
        try:
            import zipfile
            from xml.etree import ElementTree as ET
            from docx import Document

            logger.info(f"尝试使用备用方法加载文档: {filename}")

            # 方法1: 尝试修复ZIP结构
            try:
                with zipfile.ZipFile(file_path, 'r') as zf:
                    # 检查ZIP文件结构
                    file_list = zf.namelist()
                    logger.debug(f"文档 {filename} 包含的文件: {file_list}")

                    # 创建新的空白文档
                    temp_doc = Document()

                    # 尝试读取document.xml
                    if 'word/document.xml' in file_list:
                        try:
                            xml_content = zf.read('word/document.xml')
                            root = ET.fromstring(xml_content)

                            # 提取文本内容
                            text_content = self._extract_text_from_xml(root)
                            if text_content:
                                # 将提取的文本添加到新文档
                                for paragraph_text in text_content:
                                    if paragraph_text.strip():
                                        temp_doc.add_paragraph(paragraph_text)

                                logger.info(f"成功从损坏文档 {filename} 中恢复了 {len(text_content)} 个段落")
                                self._doc_cache[filename] = temp_doc
                                return temp_doc
                        except ET.ParseError as xml_e:
                            logger.warning(f"XML解析失败 {filename}: {str(xml_e)}")

                    # 如果无法提取内容，创建包含警告信息的文档
                    temp_doc.add_paragraph(f"[警告] 文档 {filename} 已损坏，无法完全恢复内容")
                    temp_doc.add_paragraph(f"原始文件路径: {file_path}")
                    temp_doc.add_paragraph(f"文件大小: {os.path.getsize(file_path)} 字节")

                    self._doc_cache[filename] = temp_doc
                    return temp_doc

            except zipfile.BadZipFile:
                logger.error(f"文档 {filename} 不是有效的ZIP文件")

            # 方法2: 尝试使用其他库或方法
            # 这里可以添加其他备用方法，比如使用python-docx2txt等

            return None

        except Exception as inner_e:
            logger.error(f"备用加载方法失败 {file_path}: {str(inner_e)}")
            return None

    def _extract_text_from_xml(self, root) -> List[str]:
        """
        从XML根节点提取文本内容

        Args:
            root: XML根节点

        Returns:
            文本段落列表
        """
        try:
            # Word文档的命名空间
            namespaces = {
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
            }

            paragraphs = []
            # 查找所有段落
            for para in root.findall('.//w:p', namespaces):
                paragraph_text = ""
                # 查找段落中的所有文本运行
                for run in para.findall('.//w:t', namespaces):
                    if run.text:
                        paragraph_text += run.text

                if paragraph_text.strip():
                    paragraphs.append(paragraph_text)

            return paragraphs

        except Exception as e:
            logger.error(f"XML文本提取失败: {str(e)}")
            return []

    def _validate_document(self, file_path: str) -> bool:
        """
        验证Word文档的基本有效性

        Args:
            file_path: 文档文件路径

        Returns:
            文档是否有效
        """
        try:
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size < 100:  # 小于100字节的docx文件几乎肯定是损坏的
                logger.warning(f"文档 {file_path} 可能已损坏 (大小: {file_size} 字节)")
                return False

            # 检查文件扩展名
            if not file_path.lower().endswith('.docx'):
                logger.warning(f"文档 {file_path} 不是.docx格式")
                return False

            # 检查是否为有效的ZIP文件（docx本质上是ZIP文件）
            import zipfile
            try:
                with zipfile.ZipFile(file_path, 'r') as zf:
                    # 检查必要的文件是否存在
                    required_files = ['[Content_Types].xml', '_rels/.rels']
                    file_list = zf.namelist()

                    for required_file in required_files:
                        if required_file not in file_list:
                            logger.warning(f"文档 {file_path} 缺少必要文件: {required_file}")
                            return False

                    # 尝试读取一个小文件来测试ZIP完整性
                    try:
                        zf.read('[Content_Types].xml')
                    except Exception as read_e:
                        logger.warning(f"文档 {file_path} ZIP结构损坏: {str(read_e)}")
                        return False

            except zipfile.BadZipFile:
                logger.warning(f"文档 {file_path} 不是有效的ZIP文件")
                return False

            return True

        except Exception as e:
            logger.error(f"验证文档 {file_path} 时发生错误: {str(e)}")
            return False

    def get_word_files_in_folder(self, folder_path: str) -> List[str]:
        """
        获取指定文件夹下的所有Word文档文件路径

        Args:
            folder_path: 文件夹路径

        Returns:
            Word文档文件路径列表
        """
        if not os.path.exists(folder_path):
            logger.warning(f"文件夹不存在: {folder_path}")
            return []

        # 搜索指定文件夹下的所有docx文件
        pattern = os.path.join(folder_path, "**/*.docx")
        word_files = glob.glob(pattern, recursive=True)

        if not word_files:
            logger.warning(f"在文件夹 {folder_path} 下未找到任何Word文档")
        else:
            logger.info(f"在文件夹 {folder_path} 下找到 {len(word_files)} 个Word文档")

        return word_files

    def search_answer(self, question_text: str, folder_path: Optional[str] = None) -> Optional[str]:
        """
        在Word文档中搜索题目答案

        Args:
            question_text: 题目文本
            folder_path: 指定的文件夹路径，如果为None则搜索默认data目录下的所有文档

        Returns:
            找到的答案，如果未找到则返回None
        """
        # 记录原始题目，用于日志
        original_question = question_text

        # 预处理题目文本，移除多余空格
        question_text = re.sub(r'\s+', ' ', question_text).strip()

        if folder_path:
            # 在指定文件夹中搜索
            word_files = self.get_word_files_in_folder(f'{self.data_dir}/{folder_path}')
            if not word_files:
                logger.warning(f"在文件夹 {folder_path} 中未找到任何Word文档")
                return None

            logger.info(f"开始在文件夹 {self.data_dir}/{folder_path} 中搜索答案")
            logger.info(f"题目: {question_text}")

            best_answer = None
            best_similarity = 0.0

            for word_file in word_files:
                try:
                    answer, similarity = self._search_in_file(word_file, question_text)
                    if answer and similarity > best_similarity:
                        best_answer = answer
                        best_similarity = similarity
                        logger.info(f"在文档 {os.path.basename(word_file)} 中找到更好的匹配，相似度: {similarity:.2f}")
                except Exception as e:
                    logger.error(f"处理文档 {word_file} 时发生错误: {str(e)}")
                    continue

            if best_answer:
                logger.info(f"最终选择的答案 (相似度: {best_similarity:.2f}): {best_answer}")
                return best_answer
            return None
        else:
            # 在默认data目录下的所有文档中搜索
            if not self.word_files:
                logger.warning(f"在 {self.data_dir} 目录下未找到任何Word文档")
                return None

            logger.info(f"开始在默认目录 {self.data_dir} 中搜索答案")
            logger.info(f"题目: {question_text}")

            best_answer = None
            best_similarity = 0.0

            for word_file in self.word_files:
                try:
                    answer, similarity = self._search_in_file(word_file, question_text)
                    if answer and similarity > best_similarity:
                        best_answer = answer
                        best_similarity = similarity
                        logger.info(f"在文档 {os.path.basename(word_file)} 中找到更好的匹配，相似度: {similarity:.2f}")
                except Exception as e:
                    logger.error(f"处理文档 {word_file} 时发生错误: {str(e)}")
                    continue

            if best_answer:
                logger.info(f"最终选择的答案 (相似度: {best_similarity:.2f}): {best_answer}")
                return best_answer
            return None

    def _search_in_file(self, file_path: str, question_text: str) -> tuple[Optional[str], float]:
        """
        在单个Word文档中搜索答案（使用相对于data_dir的路径）

        Args:
            file_path: Word文档路径
            question_text: 题目文本

        Returns:
            (找到的答案, 相似度分数)，如果未找到则返回(None, 0.0)
        """
        filename = os.path.basename(file_path)
        doc = self.load_document(filename, file_path)
        if doc:
            result = self._search_in_document(doc, question_text, filename)
            if isinstance(result, tuple) and len(result) == 2:
                # 如果返回的是元组(答案, 相似度)，直接返回
                return result
            elif result:
                # 如果只返回了答案，使用默认相似度
                return result, 0.5
        return None, 0.0

    def add_answer_marker(self, marker: str) -> None:
        """
        添加答案标记格式
        
        Args:
            marker: 答案标记文本
        """
        if marker not in self.answer_markers:
            self.answer_markers.append(marker)

    def clear_cache(self) -> None:
        """清除文件缓存"""
        self._word_files = None
        self._doc_cache.clear()
